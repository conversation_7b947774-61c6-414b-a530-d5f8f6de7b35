#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容读取工具
支持读取PDF文件的文本内容
"""

import sys
import os
from pathlib import Path

try:
    import PyPDF2
except ImportError:
    print("PyPDF2库未安装，正在尝试安装...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PyPDF2"])
    import PyPDF2

def read_pdf_content(pdf_path):
    """
    读取PDF文件内容
    
    Args:
        pdf_path (str): PDF文件路径
        
    Returns:
        str: PDF文件的文本内容
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            return f"错误：文件 '{pdf_path}' 不存在"
        
        # 打开PDF文件
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # 获取页数
            num_pages = len(pdf_reader.pages)
            print(f"PDF文件共有 {num_pages} 页")
            
            # 提取所有页面的文本
            text_content = ""
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                text_content += f"\n--- 第 {page_num + 1} 页 ---\n"
                text_content += page_text
                text_content += "\n"
            
            return text_content
            
    except Exception as e:
        return f"读取PDF时发生错误: {str(e)}"

def save_text_to_file(text_content, output_file="pdf_content.txt"):
    """
    将提取的文本保存到文件
    
    Args:
        text_content (str): 要保存的文本内容
        output_file (str): 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"文本内容已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件时发生错误: {str(e)}")

def main():
    """主函数"""
    # 默认PDF文件路径
    default_pdf = "智慧城市巡检系统-使用手册V1.2.4 (2).pdf"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = default_pdf
    
    print(f"正在读取PDF文件: {pdf_path}")
    print("-" * 50)
    
    # 读取PDF内容
    content = read_pdf_content(pdf_path)
    
    # 显示内容
    print(content)
    
    # 询问是否保存到文件
    save_choice = input("\n是否将内容保存到文本文件？(y/n): ").lower().strip()
    if save_choice in ['y', 'yes', '是']:
        output_file = input("请输入输出文件名 (默认: pdf_content.txt): ").strip()
        if not output_file:
            output_file = "pdf_content.txt"
        save_text_to_file(content, output_file)

if __name__ == "__main__":
    main()
