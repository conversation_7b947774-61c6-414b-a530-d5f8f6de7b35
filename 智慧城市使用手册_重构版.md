# 智慧城市巡检系统使用手册

**版本：** V1.2.4  
**发布时间：** 2025年2月  
**发布单位：** 广州智飞科技有限公司

---

## 目录

### 基础内容
1. [自动机场管理](#1-自动机场管理)
2. [无人机管理](#2-无人机管理)
3. [地图操作与界面导航](#3-地图操作与界面导航)
4. [事件管理基础](#4-事件管理基础)
5. [航线库管理](#5-航线库管理)
6. [区域、热点绘制与管理](#6-区域热点绘制与管理)
7. [面状航线规划](#7-面状航线规划)
8. [任务管理基础](#8-任务管理基础)
9. [计划库与任务创建](#9-计划库与任务创建)
10. [远程控制操作](#10-远程控制操作)
11. [负载控制与拍摄](#11-负载控制与拍摄)
12. [警情处置](#12-警情处置)

### 进阶内容
13. [算法库与AI功能](#13-算法库与ai功能)
14. [系统维护与版本管理](#14-系统维护与版本管理)
15. [智识联动与AI任务](#15-智识联动与ai任务)
16. [媒体库与模型库管理](#16-媒体库与模型库管理)
17. [综合实战演练](#17-综合实战演练)

### 安全规范
18. [无人机自动机场安全规范细则](#18-无人机自动机场安全规范细则)

---

## 系统简介

智慧城市巡检系统是专为智慧城市治理打造的无人机智能管控平台，具备组织（团队）、事件、模型库、媒体库、航线、任务、区域、算法库、图层、视频监控及设备的模块管理。

### 核心功能
- **多机型支持**：云平台支持多款机型作业，通过线上进行航线规划
- **远程管控**：利用机场执行飞行计划，可远程实时获取作业信息
- **AI智能识别**：系统覆盖多种AI算法如烟火识别、车辆识别、人员识别等
- **事件管理**：通过AI触发事件上报和统计，进行城市事件监控与处理
- **城市级应用**：实现城市级AI应用创新及服务运行管理

### 支持设备

#### 飞行器
- 经纬M30系列
- 经纬M350RTK
- DJI Mavic3E
- Mavic3T
- M3D
- M3TD

#### 自动机场
- 智飞系列机场
- 大疆机场
- 大疆机场2

#### 智能挂载
- 智飞系列：ABL120（喊话探照一体）、AB125D
- DJI系列：禅思ZenmuseH20T、ZenmuseP1等负载

### 威海部署情况
威海目前一共35台自动机场，分布如下：
- **环翠区**：8台
- **高区**：4台
- **经区**：4台
- **临港**：2台
- **荣成**：6台
- **乳山**：4台
- **文登**：6台
- **机动**：1台

按机场类型分：
- **DCOK3充电机场**：8台
- **M4换电机场**：27台

### 系统基础配置

#### 运行要求
运行智慧城市巡检系统的浏览器仅支持谷歌浏览器、Edge浏览器，对其他浏览器功能表现不做承诺。

#### 系统角色与权限
智慧城市巡检系统包括系统管理员，及组织层级下的组织管理员、设备维护员、数据维护员、组织成员五种角色，组织下可包含多个子组织。不同角色的用户拥有不同的操作权限。

**用户角色与权限**
1. **系统管理员**：具备系统的所有管理及操作权限
2. **组织管理员**：管理加入组织的用户及设备、航线、区域、计划、图层等资源
3. **设备维护员**：管理系统内的设备
4. **数据维护员**：具备系统除设备外的数据维护权限
5. **组织成员**：仅具备系统的查看权限

#### 登录注册
用户使用浏览器访问平台地址，输入正确的账号密码并登录智慧城市巡检系统。
- 用户账号由系统管理员或组织管理员创建
- 账号密码请询问管理员
- 首次登录系统将强制进入修改密码页面

---

## 基础内容

## 1. 自动机场管理

### 1.1 机场接入指南

#### MQTT连接配置

**智飞系列机库接入智慧城市**
1. 点击左上角按钮打开侧边栏
2. 点击"服务器设置"打开mqtt配置界面
3. 在配置界面中填入mqtt服务对应的ip、端口、账号和密码
4. 当左上角"服务器连接"显示"已连接"或日志显示连接成功则机场上线成功

**大疆系列机库接入智慧城市**
1. 使用数据线连接机场的USB-A接口至遥控器的USB-C口
2. 弹出机场部署页面后，根据页面指引进行机场部署
3. 在云服务配置界面切换第三方服务
4. 依次填入mqtt服务对应的网关地址、账号和密码、组织ID和设备绑定码
5. 填写完毕后点击"确定"，将机场绑定至平台
6. 当云服务配置界面显示"已绑定第三方服务"则机场上线成功

### 1.2 自动机场管理功能

#### 添加自动机场
1. 点击设备列表上方的添加按钮
2. 录入设备名称、设备SN完成添加

#### 查看挂载详情
- 该挂载详情归属于自动机场所挂载的无人机
- 点击展开悬浮列表，可查看该无人机的挂载信息
- 包括挂载名称、挂载型号

#### 变更机场信息
- 管理员可在机场管理页面点击"编辑"，修改机场设备名称、设备型号
- 或点击"删除"，删除自动机场信息

#### 机场使用人管理
- 点击"使用人"，该自动机场的使用人为选中组织
- 点击"指定人"按钮，该自动机场的使用人为选中用户
- 当机场的使用人为"指定人"时，该用户的组织变更不影响对该设备的原有使用权限

#### 查看告警信息
- 点击查看机场的告警信息
- 告警信息包括告警开始与结束时间、告警等级、设备、错误码、告警内容

### 1.3 机场状态监控

#### 机场状态类型
- 设备空闲中
- 现场调试中
- 远程调试中
- 设备升级中
- 设备作业中
- 设备已离线

#### 机场任务状态
- 无任务
- 待执行
- 执行中
- 执行失败
- 执行终止
- 执行成功

---

## 2. 无人机管理

### 2.1 Pilot上云绑定

#### 飞行器绑定流程
用户可通过Pilot上云绑定飞行器至当前组织。确保遥控器已接入互联网，运行DJI Pilot2进入首页。

**操作步骤：**
1. **进入云服务界面**
   - 在首页左上角找到并点击"云服务"按钮，进入云服务界面

2. **选择云平台**
   - 在云服务界面中，点击选择"开放平台"作为要使用的云平台

3. **连接云平台**
   - 进入开放平台后，手动输入URL链接
   - URL链接和登录账号可在云平台：设备管理-无人机-绑定说明中复制
   - 输入完成后，点击页面右上角的"连接"按钮

4. **登录云平台**
   - 成功连接后，在登录页面手动输入用户名和密码
   - 点击"登录"按钮，即可通过遥控器连接智飞智慧城市云平台

5. **完成绑定**
   - 成功连接智飞智慧城市云平台后，在设备管理-无人机界面点击添加
   - 填入设备名称、SN新增
   - 绑定成功后管理员可在网页端设备管理页面查看和管理该飞行器

### 2.2 单兵无人机接入

#### 无人机管理功能

**添加无人机**
1. 点击设备列表上方的添加按钮
2. 录入设备名称、设备SN完成添加

**挂载详情**
- 点击展开悬浮列表，可查看该无人机的挂载详情
- 包括挂载名称、挂载型号

**变更无人机信息**
- 管理员可在无人机管理页面编辑无人机信息或删除无人机
- 可修改设备名称、设备型号等信息

### 2.3 无人机状态监控

#### 无人机状态类型
- 不在舱内
- 舱内关机
- 离线
- 开机中
- 待机
- 起飞准备中
- 起飞准备完成
- 手动飞行
- 航线飞行
- 返航
- 降落

#### 实时数据监控
显示无人机的实时数据，包括：
- 图传状态
- 搜星质量
- 电池电量
- 海拔高度
- 相对地面高度
- 水平速度
- 离home点距离
- 负载型号

---

## 3. 地图操作与界面导航

### 3.1 地图界面功能

#### 机场列表
- 该面板可查看机场在线设备列表
- 点击地图中的机库图标可展开机库地图直播
- 可切换机库内外部推流（智飞系列机库、大疆机库）
- 支持补光灯开/关、切换清晰度、全屏、刷新

#### 地图元素设置
点击开启机场设置，可开启或关闭：
- 地图直播
- 航线轨迹
- 实时云台视角
- AR投影

### 3.2 快速响应功能

#### 快速响应操作
用户可右键点击机场立体图标进入快速响应选点，类型包括：
- 一键全景
- 单点智拍
- 一键正射

#### 转换为面状航线
可右键点击区域转换为测区，将绘制区域转换为面状航线规划。

### 3.3 地图工具使用

#### 地图操作功能
地图页面右下角可进行如下操作：

1. **搜索功能**
   - 点击切换搜索地点和坐标

2. **测量工具**
   - 展开测量工具，测量地图上的物体
   - 可选择两个点之间测量高度差
   - 绘制多边形测量其闭合区域的总面积
   - 测量多个点之间的距离
   - 右键清除地图上的测量记录

3. **飞行区设置**
   - 开启显示自定义飞行区或限飞区
   - 可规划自定义飞行区

4. **事件显示**
   - 开启显示业务元素图标的事件等级
   - 事件处理状态
   - 事件显/隐三维地球上的事件图标

5. **地图模式**
   - 切换地图模式为卫星地图或标准地图
   - 可开启高清模型功能显示清晰度
   - 切换天空为蓝天或晴天、晚霞或黑夜样式
   - 可开启地下模式、地表透明度

---

## 4. 事件管理基础

### 4.1 事件管理概念

#### 事件类型与处理
智慧城市巡检系统通过AI算法自动识别异常情况并触发事件上报，同时支持手动录入事件。

#### 事件处理流程
1. **事件发现**：AI识别或手动发现异常情况
2. **事件上报**：系统自动或手动创建事件记录
3. **事件分析**：查看事件详情和相关数据
4. **事件处理**：采取相应的处理措施
5. **事件结束**：完成处理后结束事件

### 4.2 事件管理操作

#### 事件查看与分析
- 在事件管理页面可查看所有事件列表
- 点击事件可查看详细信息
- 包括事件时间、地点、类型、处理状态等

#### 事件状态管理
- 待处理
- 处理中
- 已处理
- 已结束

#### 事件统计功能
- 按时间段统计事件数量
- 按事件类型分类统计
- 按处理状态统计
- 生成事件分析报告
