<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧城市巡检系统使用手册 V1.2.4</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .header .meta {
            color: #666;
            font-size: 1.1em;
        }
        
        .toc {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        
        .toc h2 {
            color: #007bff;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .toc-section {
            margin-bottom: 20px;
        }
        
        .toc-section h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .toc-list {
            list-style: none;
            padding-left: 0;
        }
        
        .toc-list li {
            margin-bottom: 8px;
            padding-left: 20px;
        }
        
        .toc-list a {
            color: #007bff;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc-list a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        
        .content-section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 8px;
            background-color: #fff;
            border: 1px solid #e9ecef;
        }
        
        .content-section h2 {
            color: #007bff;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .content-section h3 {
            color: #495057;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }
        
        .content-section h4 {
            color: #6c757d;
            font-size: 1.2em;
            margin: 20px 0 10px 0;
        }
        
        .content-section p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .content-section ul, .content-section ol {
            margin-bottom: 15px;
            padding-left: 25px;
        }
        
        .content-section li {
            margin-bottom: 8px;
        }
        
        .highlight-box {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .danger-box {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding-left: 40px;
            position: relative;
        }
        
        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background-color: #007bff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .card h4 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-online {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-working {
            background-color: #fff3cd;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            display: none;
            font-size: 1.2em;
            transition: background-color 0.3s;
        }
        
        .back-to-top:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>智慧城市巡检系统使用手册</h1>
            <div class="meta">
                <strong>版本：</strong>V1.2.4 &nbsp;&nbsp;
                <strong>发布时间：</strong>2025年2月 &nbsp;&nbsp;
                <strong>发布单位：</strong>广州智飞科技有限公司
            </div>
        </header>

        <nav class="toc">
            <h2>📋 目录</h2>
            
            <div class="toc-section">
                <h3>🔧 基础内容</h3>
                <ul class="toc-list">
                    <li><a href="#section-1">1. 自动机场管理</a></li>
                    <li><a href="#section-2">2. 无人机管理</a></li>
                    <li><a href="#section-3">3. 地图操作与界面导航</a></li>
                    <li><a href="#section-4">4. 事件管理基础</a></li>
                    <li><a href="#section-5">5. 航线库管理</a></li>
                    <li><a href="#section-6">6. 区域、热点绘制与管理</a></li>
                    <li><a href="#section-7">7. 面状航线规划</a></li>
                    <li><a href="#section-8">8. 任务管理基础</a></li>
                    <li><a href="#section-9">9. 计划库与任务创建</a></li>
                    <li><a href="#section-10">10. 远程控制操作</a></li>
                    <li><a href="#section-11">11. 负载控制与拍摄</a></li>
                    <li><a href="#section-12">12. 警情处置</a></li>
                </ul>
            </div>
            
            <div class="toc-section">
                <h3>🚀 进阶内容</h3>
                <ul class="toc-list">
                    <li><a href="#section-13">13. 算法库与AI功能</a></li>
                    <li><a href="#section-14">14. 系统维护与版本管理</a></li>
                    <li><a href="#section-15">15. 智识联动与AI任务</a></li>
                    <li><a href="#section-16">16. 媒体库与模型库管理</a></li>
                    <li><a href="#section-17">17. 综合实战演练</a></li>
                </ul>
            </div>
            
            <div class="toc-section">
                <h3>⚠️ 安全规范</h3>
                <ul class="toc-list">
                    <li><a href="#section-18">18. 无人机自动机场安全规范细则</a></li>
                </ul>
            </div>
        </nav>

        <section class="content-section">
            <h2>🌟 系统简介</h2>
            <p>智慧城市巡检系统是专为智慧城市治理打造的无人机智能管控平台，具备组织（团队）、事件、模型库、媒体库、航线、任务、区域、算法库、图层、视频监控及设备的模块管理。</p>
            
            <h3>核心功能</h3>
            <div class="grid-2">
                <div class="card">
                    <h4>🛩️ 多机型支持</h4>
                    <p>云平台支持多款机型作业，通过线上进行航线规划</p>
                </div>
                <div class="card">
                    <h4>🎮 远程管控</h4>
                    <p>利用机场执行飞行计划，可远程实时获取作业信息</p>
                </div>
                <div class="card">
                    <h4>🤖 AI智能识别</h4>
                    <p>系统覆盖多种AI算法如烟火识别、车辆识别、人员识别等</p>
                </div>
                <div class="card">
                    <h4>📊 事件管理</h4>
                    <p>通过AI触发事件上报和统计，进行城市事件监控与处理</p>
                </div>
            </div>
            
            <h3>支持设备</h3>
            <div class="grid-3">
                <div class="card">
                    <h4>✈️ 飞行器</h4>
                    <ul>
                        <li>经纬M30系列</li>
                        <li>经纬M350RTK</li>
                        <li>DJI Mavic3E</li>
                        <li>Mavic3T</li>
                        <li>M3D</li>
                        <li>M3TD</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>🏭 自动机场</h4>
                    <ul>
                        <li>智飞系列机场</li>
                        <li>大疆机场</li>
                        <li>大疆机场2</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>📷 智能挂载</h4>
                    <ul>
                        <li>智飞系列：ABL120、AB125D</li>
                        <li>DJI系列：ZenmuseH20T、ZenmuseP1等</li>
                    </ul>
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>🏙️ 威海部署情况</h3>
                <p><strong>威海目前一共35台自动机场，分布如下：</strong></p>
                <div class="grid-2">
                    <div>
                        <h4>按区域分布：</h4>
                        <ul>
                            <li><strong>环翠区：</strong>8台</li>
                            <li><strong>高区：</strong>4台</li>
                            <li><strong>经区：</strong>4台</li>
                            <li><strong>临港：</strong>2台</li>
                            <li><strong>荣成：</strong>6台</li>
                            <li><strong>乳山：</strong>4台</li>
                            <li><strong>文登：</strong>6台</li>
                            <li><strong>机动：</strong>1台</li>
                        </ul>
                    </div>
                    <div>
                        <h4>按机场类型分：</h4>
                        <ul>
                            <li><strong>DCOK3充电机场：</strong>8台</li>
                            <li><strong>M4换电机场：</strong>27台</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="content-section">
            <h2>⚙️ 系统基础配置</h2>

            <h3>运行要求</h3>
            <div class="warning-box">
                <p><strong>⚠️ 浏览器兼容性：</strong>运行智慧城市巡检系统的浏览器仅支持谷歌浏览器、Edge浏览器，对其他浏览器功能表现不做承诺。</p>
            </div>

            <h3>系统角色与权限</h3>
            <p>智慧城市巡检系统包括系统管理员，及组织层级下的组织管理员、设备维护员、数据维护员、组织成员五种角色，组织下可包含多个子组织。不同角色的用户拥有不同的操作权限。</p>

            <div class="grid-2">
                <div class="card">
                    <h4>👑 系统管理员</h4>
                    <p>具备系统的所有管理及操作权限</p>
                </div>
                <div class="card">
                    <h4>👨‍💼 组织管理员</h4>
                    <p>管理加入组织的用户及设备、航线、区域、计划、图层等资源</p>
                </div>
                <div class="card">
                    <h4>🔧 设备维护员</h4>
                    <p>管理系统内的设备</p>
                </div>
                <div class="card">
                    <h4>📊 数据维护员</h4>
                    <p>具备系统除设备外的数据维护权限</p>
                </div>
            </div>
            <div class="card">
                <h4>👥 组织成员</h4>
                <p>仅具备系统的查看权限</p>
            </div>

            <h3>登录注册</h3>
            <ul>
                <li>用户使用浏览器访问平台地址，输入正确的账号密码并登录智慧城市巡检系统</li>
                <li>用户账号由系统管理员或组织管理员创建</li>
                <li>账号密码请询问管理员</li>
                <li>首次登录系统将强制进入修改密码页面</li>
            </ul>
        </section>

        <section class="content-section" id="section-1">
            <h2>🏭 1. 自动机场管理</h2>

            <h3>1.1 机场接入指南</h3>

            <h4>MQTT连接配置</h4>

            <div class="highlight-box">
                <h4>智飞系列机库接入智慧城市</h4>
                <ol class="step-list">
                    <li>点击左上角按钮打开侧边栏</li>
                    <li>点击"服务器设置"打开mqtt配置界面</li>
                    <li>在配置界面中填入mqtt服务对应的ip、端口、账号和密码</li>
                    <li>当左上角"服务器连接"显示"已连接"或日志显示连接成功则机场上线成功</li>
                </ol>
            </div>

            <div class="highlight-box">
                <h4>大疆系列机库接入智慧城市</h4>
                <ol class="step-list">
                    <li>使用数据线连接机场的USB-A接口至遥控器的USB-C口</li>
                    <li>弹出机场部署页面后，根据页面指引进行机场部署</li>
                    <li>在云服务配置界面切换第三方服务</li>
                    <li>依次填入mqtt服务对应的网关地址、账号和密码、组织ID和设备绑定码</li>
                    <li>填写完毕后点击"确定"，将机场绑定至平台</li>
                    <li>当云服务配置界面显示"已绑定第三方服务"则机场上线成功</li>
                </ol>
            </div>

            <h3>1.2 自动机场管理功能</h3>

            <h4>添加自动机场</h4>
            <ol>
                <li>点击设备列表上方的添加按钮</li>
                <li>录入设备名称、设备SN完成添加</li>
            </ol>

            <h4>查看挂载详情</h4>
            <ul>
                <li>该挂载详情归属于自动机场所挂载的无人机</li>
                <li>点击展开悬浮列表，可查看该无人机的挂载信息</li>
                <li>包括挂载名称、挂载型号</li>
            </ul>

            <h4>变更机场信息</h4>
            <ul>
                <li>管理员可在机场管理页面点击"编辑"，修改机场设备名称、设备型号</li>
                <li>或点击"删除"，删除自动机场信息</li>
            </ul>

            <h4>机场使用人管理</h4>
            <ul>
                <li>点击"使用人"，该自动机场的使用人为选中组织</li>
                <li>点击"指定人"按钮，该自动机场的使用人为选中用户</li>
                <li>当机场的使用人为"指定人"时，该用户的组织变更不影响对该设备的原有使用权限</li>
            </ul>

            <h4>查看告警信息</h4>
            <ul>
                <li>点击查看机场的告警信息</li>
                <li>告警信息包括告警开始与结束时间、告警等级、设备、错误码、告警内容</li>
            </ul>

            <h3>1.3 机场状态监控</h3>

            <div class="grid-2">
                <div class="card">
                    <h4>机场状态类型</h4>
                    <ul>
                        <li><span class="status-indicator status-online">设备空闲中</span></li>
                        <li><span class="status-indicator status-working">现场调试中</span></li>
                        <li><span class="status-indicator status-working">远程调试中</span></li>
                        <li><span class="status-indicator status-working">设备升级中</span></li>
                        <li><span class="status-indicator status-working">设备作业中</span></li>
                        <li><span class="status-indicator status-offline">设备已离线</span></li>
                    </ul>
                </div>
                <div class="card">
                    <h4>机场任务状态</h4>
                    <ul>
                        <li><span class="status-indicator status-online">无任务</span></li>
                        <li><span class="status-indicator status-working">待执行</span></li>
                        <li><span class="status-indicator status-working">执行中</span></li>
                        <li><span class="status-indicator status-offline">执行失败</span></li>
                        <li><span class="status-indicator status-offline">执行终止</span></li>
                        <li><span class="status-indicator status-online">执行成功</span></li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="content-section" id="section-2">
            <h2>🛩️ 2. 无人机管理</h2>

            <h3>2.1 Pilot上云绑定</h3>

            <h4>飞行器绑定流程</h4>
            <p>用户可通过Pilot上云绑定飞行器至当前组织。确保遥控器已接入互联网，运行DJI Pilot2进入首页。</p>

            <div class="highlight-box">
                <h4>操作步骤：</h4>
                <ol class="step-list">
                    <li><strong>进入云服务界面</strong><br>在首页左上角找到并点击"云服务"按钮，进入云服务界面</li>
                    <li><strong>选择云平台</strong><br>在云服务界面中，点击选择"开放平台"作为要使用的云平台</li>
                    <li><strong>连接云平台</strong><br>进入开放平台后，手动输入URL链接<br>URL链接和登录账号可在云平台：设备管理-无人机-绑定说明中复制<br>输入完成后，点击页面右上角的"连接"按钮</li>
                    <li><strong>登录云平台</strong><br>成功连接后，在登录页面手动输入用户名和密码<br>点击"登录"按钮，即可通过遥控器连接智飞智慧城市云平台</li>
                    <li><strong>完成绑定</strong><br>成功连接智飞智慧城市云平台后，在设备管理-无人机界面点击添加<br>填入设备名称、SN新增<br>绑定成功后管理员可在网页端设备管理页面查看和管理该飞行器</li>
                </ol>
            </div>

            <h3>2.2 单兵无人机接入</h3>

            <h4>无人机管理功能</h4>

            <div class="grid-3">
                <div class="card">
                    <h4>添加无人机</h4>
                    <ol>
                        <li>点击设备列表上方的添加按钮</li>
                        <li>录入设备名称、设备SN完成添加</li>
                    </ol>
                </div>
                <div class="card">
                    <h4>挂载详情</h4>
                    <ul>
                        <li>点击展开悬浮列表，可查看该无人机的挂载详情</li>
                        <li>包括挂载名称、挂载型号</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>变更无人机信息</h4>
                    <ul>
                        <li>管理员可在无人机管理页面编辑无人机信息或删除无人机</li>
                        <li>可修改设备名称、设备型号等信息</li>
                    </ul>
                </div>
            </div>

            <h3>2.3 无人机状态监控</h3>

            <div class="grid-2">
                <div class="card">
                    <h4>无人机状态类型</h4>
                    <ul>
                        <li><span class="status-indicator status-offline">不在舱内</span></li>
                        <li><span class="status-indicator status-offline">舱内关机</span></li>
                        <li><span class="status-indicator status-offline">离线</span></li>
                        <li><span class="status-indicator status-working">开机中</span></li>
                        <li><span class="status-indicator status-online">待机</span></li>
                        <li><span class="status-indicator status-working">起飞准备中</span></li>
                        <li><span class="status-indicator status-online">起飞准备完成</span></li>
                        <li><span class="status-indicator status-working">手动飞行</span></li>
                        <li><span class="status-indicator status-working">航线飞行</span></li>
                        <li><span class="status-indicator status-working">返航</span></li>
                        <li><span class="status-indicator status-working">降落</span></li>
                    </ul>
                </div>
                <div class="card">
                    <h4>实时数据监控</h4>
                    <p>显示无人机的实时数据，包括：</p>
                    <ul>
                        <li>图传状态</li>
                        <li>搜星质量</li>
                        <li>电池电量</li>
                        <li>海拔高度</li>
                        <li>相对地面高度</li>
                        <li>水平速度</li>
                        <li>离home点距离</li>
                        <li>负载型号</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="content-section" id="section-3">
            <h2>🗺️ 3. 地图操作与界面导航</h2>

            <h3>3.1 地图界面功能</h3>

            <h4>机场列表</h4>
            <ul>
                <li>该面板可查看机场在线设备列表</li>
                <li>点击地图中的机库图标可展开机库地图直播</li>
                <li>可切换机库内外部推流（智飞系列机库、大疆机库）</li>
                <li>支持补光灯开/关、切换清晰度、全屏、刷新</li>
            </ul>

            <h4>地图元素设置</h4>
            <p>点击开启机场设置，可开启或关闭：</p>
            <div class="grid-2">
                <div class="card">
                    <ul>
                        <li>地图直播</li>
                        <li>航线轨迹</li>
                    </ul>
                </div>
                <div class="card">
                    <ul>
                        <li>实时云台视角</li>
                        <li>AR投影</li>
                    </ul>
                </div>
            </div>

            <h3>3.2 快速响应功能</h3>

            <h4>快速响应操作</h4>
            <p>用户可右键点击机场立体图标进入快速响应选点，类型包括：</p>
            <div class="grid-3">
                <div class="card">
                    <h4>🌐 一键全景</h4>
                    <p>快速进行全景拍摄</p>
                </div>
                <div class="card">
                    <h4>📸 单点智拍</h4>
                    <p>对指定点位进行智能拍摄</p>
                </div>
                <div class="card">
                    <h4>📐 一键正射</h4>
                    <p>进行正射影像采集</p>
                </div>
            </div>

            <h4>转换为面状航线</h4>
            <p>可右键点击区域转换为测区，将绘制区域转换为面状航线规划。</p>

            <h3>3.3 地图工具使用</h3>

            <h4>地图操作功能</h4>
            <p>地图页面右下角可进行如下操作：</p>

            <div class="grid-2">
                <div class="card">
                    <h4>🔍 搜索功能</h4>
                    <p>点击切换搜索地点和坐标</p>
                </div>
                <div class="card">
                    <h4>📏 测量工具</h4>
                    <ul>
                        <li>展开测量工具，测量地图上的物体</li>
                        <li>可选择两个点之间测量高度差</li>
                        <li>绘制多边形测量其闭合区域的总面积</li>
                        <li>测量多个点之间的距离</li>
                        <li>右键清除地图上的测量记录</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>🚫 飞行区设置</h4>
                    <ul>
                        <li>开启显示自定义飞行区或限飞区</li>
                        <li>可规划自定义飞行区</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>📊 事件显示</h4>
                    <ul>
                        <li>开启显示业务元素图标的事件等级</li>
                        <li>事件处理状态</li>
                        <li>事件显/隐三维地球上的事件图标</li>
                    </ul>
                </div>
            </div>

            <div class="highlight-box">
                <h4>🎨 地图模式</h4>
                <ul>
                    <li>切换地图模式为卫星地图或标准地图</li>
                    <li>可开启高清模型功能显示清晰度</li>
                    <li>切换天空为蓝天或晴天、晚霞或黑夜样式</li>
                    <li>可开启地下模式、地表透明度</li>
                </ul>
            </div>
        </section>

        <section class="content-section" id="section-4">
            <h2>📋 4. 事件管理基础</h2>

            <h3>4.1 事件管理概念</h3>

            <h4>事件类型与处理</h4>
            <p>智慧城市巡检系统通过AI算法自动识别异常情况并触发事件上报，同时支持手动录入事件。</p>

            <h4>事件处理流程</h4>
            <div class="highlight-box">
                <ol class="step-list">
                    <li><strong>事件发现</strong>：AI识别或手动发现异常情况</li>
                    <li><strong>事件上报</strong>：系统自动或手动创建事件记录</li>
                    <li><strong>事件分析</strong>：查看事件详情和相关数据</li>
                    <li><strong>事件处理</strong>：采取相应的处理措施</li>
                    <li><strong>事件结束</strong>：完成处理后结束事件</li>
                </ol>
            </div>

            <h3>4.2 事件管理操作</h3>

            <h4>事件查看与分析</h4>
            <ul>
                <li>在事件管理页面可查看所有事件列表</li>
                <li>点击事件可查看详细信息</li>
                <li>包括事件时间、地点、类型、处理状态等</li>
            </ul>

            <div class="grid-2">
                <div class="card">
                    <h4>事件状态管理</h4>
                    <ul>
                        <li><span class="status-indicator status-working">待处理</span></li>
                        <li><span class="status-indicator status-working">处理中</span></li>
                        <li><span class="status-indicator status-online">已处理</span></li>
                        <li><span class="status-indicator status-online">已结束</span></li>
                    </ul>
                </div>
                <div class="card">
                    <h4>事件统计功能</h4>
                    <ul>
                        <li>按时间段统计事件数量</li>
                        <li>按事件类型分类统计</li>
                        <li>按处理状态统计</li>
                        <li>生成事件分析报告</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="content-section" id="section-18">
            <h2>⚠️ 18. 无人机自动机场安全规范细则</h2>

            <div class="danger-box">
                <h3>⚠️ 重要提醒</h3>
                <p>安全是无人机操作的首要原则，请严格遵守以下安全规范，确保人员和设备安全。</p>
            </div>

            <h3>一、环境条件（核心安全前提）</h3>

            <h4>气象条件监测要求：</h4>
            <div class="grid-2">
                <div class="card">
                    <h4>🌪️ 风速</h4>
                    <ul>
                        <li><strong>起飞风速：</strong>≤6.6m/s</li>
                        <li><strong>任务过程中风速：</strong>≤10m/s</li>
                    </ul>
                </div>
                <div class="card">
                    <h4>🌧️ 降水</h4>
                    <p>禁止雨雪天气起飞</p>
                </div>
                <div class="card">
                    <h4>🌫️ 能见度</h4>
                    <p>禁止大雾、霾、沙尘等低能见度环境飞行</p>
                </div>
                <div class="card">
                    <h4>🌡️ 温度</h4>
                    <p>工作温区：-10°~40°</p>
                </div>
            </div>

            <h3>二、航线规划核心注意事项</h3>

            <h4>环境与障碍物安全：</h4>
            <div class="warning-box">
                <ul>
                    <li><strong>静态障碍排查：</strong>航线高度需高于周边最高障碍物50米以上</li>
                    <li><strong>动态环境适配：</strong>避开大风、降水、大雾天气</li>
                    <li><strong>空域管理：</strong>避开民航航线、军用空域、重点目标区域</li>
                </ul>
            </div>

            <h4>飞行限制：</h4>
            <div class="danger-box">
                <ul>
                    <li>❌ <strong>不建议跨越高压线作业</strong></li>
                    <li>❌ <strong>不建议跨越水面作业</strong></li>
                    <li>🚫 <strong>禁止跨铁路飞行</strong></li>
                    <li>📏 <strong>飞行高度原则上不超过真高120m</strong></li>
                </ul>
            </div>

            <h4>任务执行安全：</h4>
            <div class="highlight-box">
                <ul>
                    <li>确保有值班人员实时监控</li>
                    <li>风速异常时立即返航</li>
                    <li>信号不佳时升高飞行器高度</li>
                    <li>关注飞行电量，避免极限电量返航</li>
                </ul>
            </div>

            <h3>三、操作人员要求</h3>

            <div class="warning-box">
                <ul>
                    <li>✅ 需持有民用无人机驾驶员执照或警航证</li>
                    <li>🔄 执照有效期内每2年进行复训考核</li>
                    <li>🚫 禁止酒后、疲劳状态操作无人机</li>
                </ul>
            </div>
        </section>

        <button class="back-to-top" onclick="scrollToTop()">↑</button>
    </div>

    <script>
        // 返回顶部功能
        window.onscroll = function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                backToTopBtn.style.display = "block";
            } else {
                backToTopBtn.style.display = "none";
            }
        };

        function scrollToTop() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
