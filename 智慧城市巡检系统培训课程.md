# 智慧城市巡检系统培训课程

## 课程概述
**培训时长：** 2.5天（20学时）  
**培训对象：** 智慧城市巡检系统用户、管理员、操作员  
**培训目标：** 掌握智慧城市巡检系统的完整操作流程，能够独立进行系统管理、设备操作、任务执行和数据分析

---

## 第一天上午（4学时）：系统基础与环境搭建

### 第一节：系统概述与架构（1学时）
**学习目标：** 了解智慧城市巡检系统的整体架构和核心功能

#### 理论讲解（40分钟）
1. **系统简介**
   - 智慧城市巡检系统功能模块介绍
   - 组织（团队）、事件、模型库、媒体库、航线、任务、区域管理
   - AI算法应用：烟火识别、车辆识别、人员识别
   - 城市级AI应用创新及服务运行管理

2. **系统架构**
   - 云平台架构设计
   - 无人机智能管控平台组成
   - 数据流转机制

3. **支持设备清单**
   - 飞行器：经纬M30系列、经纬M350RTK、DJI Mavic3E、Mavic3T、M3D、M3TD
   - 机场：智飞系列机场、大疆机场、大疆机场2
   - 智能挂载：ABL120、AB125D、ZenmuseH20T、ZenmuseP1等

#### 实操演示（20分钟）
- 系统界面导览
- 功能模块快速浏览

### 第二节：系统环境与登录（1学时）
**学习目标：** 掌握系统登录、环境配置和基础设置

#### 理论讲解（30分钟）
1. **运行要求**
   - 浏览器兼容性：谷歌浏览器、Edge浏览器
   - 网络环境要求

2. **用户角色与权限体系**
   - 系统管理员：系统所有管理及操作权限
   - 组织管理员：管理组织用户及设备、航线、区域、计划、图层
   - 设备维护员：管理系统内设备
   - 数据维护员：系统除设备外的数据维护权限
   - 组织成员：系统查看权限

#### 实操练习（30分钟）
- 系统登录操作
- 首次登录密码修改
- 个人菜单设置
- 系统语言切换
- 快速指引功能使用

### 第三节：组织管理与用户管理（1学时）
**学习目标：** 掌握组织架构搭建和用户权限管理

#### 理论讲解（30分钟）
1. **组织管理**
   - 组织创建与层级结构
   - 组织重命名与删除
   - 根组织管理规则

2. **用户管理**
   - 用户创建流程
   - 批量用户导入
   - 用户信息变更
   - 密码重置操作

#### 实操练习（30分钟）
- 创建组织结构
- 添加用户账号
- 分配用户角色
- 批量用户管理操作

### 第四节：设备接入与配置（1学时）
**学习目标：** 掌握机场和无人机的接入配置方法

#### 理论讲解（30分钟）
1. **MQTT连接原理**
   - 智飞系列机库接入流程
   - 大疆系列机库接入流程
   - 连接状态监控

2. **设备绑定流程**
   - 机场设备添加
   - 无人机绑定操作
   - 设备状态监控

#### 实操练习（30分钟）
- MQTT服务配置
- 机场连接测试
- 无人机绑定演示
- 设备状态查看

---

## 第一天下午（4学时）：设备管理与基础操作

### 第五节：飞行器管理（1学时）
**学习目标：** 掌握飞行器的管理和监控操作

#### 理论讲解（30分钟）
1. **飞行器绑定**
   - Pilot上云绑定流程
   - DJI Pilot2操作步骤
   - 云平台连接配置

2. **飞行器状态监控**
   - 设备状态类型
   - 实时数据监控
   - 告警信息处理

#### 实操练习（30分钟）
- 飞行器绑定操作
- 设备信息查看
- 挂载详情检查
- 状态监控实践

### 第六节：自动机场管理（1学时）
**学习目标：** 掌握自动机场的管理和维护操作

#### 理论讲解（30分钟）
1. **机场管理功能**
   - 机场添加与配置
   - 挂载信息查看
   - 机场信息变更

2. **机场使用权限**
   - 使用人管理
   - 权限分配规则
   - 告警信息处理

#### 实操练习（30分钟）
- 添加自动机场
- 查看挂载详情
- 设置使用权限
- 告警信息查看

### 第七节：地图操作与界面导航（1学时）
**学习目标：** 掌握地图界面的操作和导航功能

#### 理论讲解（30分钟）
1. **地图界面功能**
   - 机场列表查看
   - 地图元素设置
   - 快速响应功能

2. **地图工具使用**
   - 搜索功能
   - 测量工具
   - 飞行区设置

#### 实操练习（30分钟）
- 地图导航操作
- 机场信息查看
- 快速响应测试
- 测量工具使用

### 第八节：事件管理基础（1学时）
**学习目标：** 了解事件管理的基本概念和操作

#### 理论讲解（30分钟）
1. **事件管理概念**
   - 事件类型与等级
   - 事件处理流程
   - AI触发事件机制

2. **事件处理操作**
   - 事件查看与分析
   - 事件状态管理
   - 事件统计功能

#### 实操练习（30分钟）
- 事件列表查看
- 事件详情分析
- 事件处理操作
- 事件统计查看

---

## 第二天上午（4学时）：航线规划与任务管理

### 第九节：区域绘制与管理（1学时）
**学习目标：** 掌握区域绘制和热点管理技能

#### 理论讲解（30分钟）
1. **区域绘制功能**
   - 区域类型选择
   - 区域信息设置
   - 区域编辑操作

2. **热点管理**
   - 热点类型：点、线、面
   - 热点信息配置
   - 附件与详情添加

#### 实操练习（30分钟）
- 绘制不同类型区域
- 设置区域属性
- 添加热点标记
- 关联区域与热点

### 第十节：航线库管理（1学时）
**学习目标：** 掌握航线的创建、编辑和管理

#### 理论讲解（30分钟）
1. **航线导入与创建**
   - KMZ文件导入
   - 航线创建流程
   - 航线类型选择

2. **航点航线编辑**
   - 航点列表管理
   - 动作编辑器使用
   - 模拟飞行功能

#### 实操练习（30分钟）
- 导入KMZ航线文件
- 创建新航线
- 编辑航点动作
- 模拟飞行测试

### 第十一节：面状航线规划（1学时）
**学习目标：** 掌握正射和倾斜航线的规划技能

#### 理论讲解（30分钟）
1. **面状航线类型**
   - 正射航线规划
   - 倾斜航线规划
   - 测绘区域设置

2. **航线参数配置**
   - GSD设置
   - 航线高度模式
   - 重叠率配置
   - 飞行速度设置

#### 实操练习（30分钟）
- 规划正射航线
- 设置倾斜航线
- 配置航线参数
- 预览航线效果

### 第十二节：计划库与任务创建（1学时）
**学习目标：** 掌握飞行计划的创建和管理

#### 理论讲解（30分钟）
1. **计划创建要素**
   - 任务类型：普通任务、AI任务、快速建模
   - 执行设备选择
   - 关联区域设置
   - 任务策略配置

2. **任务执行策略**
   - 立即执行
   - 单次定时
   - 重复定时
   - 连续执行

#### 实操练习（30分钟）
- 创建普通任务
- 设置AI任务
- 配置定时任务
- 任务执行监控

---

## 第二天下午（4学时）：远程控制与数据管理

### 第十三节：远程控制操作（1学时）
**学习目标：** 掌握无人机的远程控制技能

#### 理论讲解（30分钟）
1. **飞行控制权获取**
   - 控制权申请流程
   - 飞行参数设置
   - 安全操作规范

2. **远程控制功能**
   - 键盘快捷键操作
   - 指定位置飞行
   - 云台控制操作

#### 实操练习（30分钟）
- 获取飞行控制权
- 远程控制飞行
- 云台操作练习
- 安全操作演练

### 第十四节：负载控制与拍摄（1学时）
**学习目标：** 掌握负载设备的控制和拍摄功能

#### 理论讲解（30分钟）
1. **负载控制功能**
   - 相机控制操作
   - 红外相机使用
   - PSDK设备控制

2. **拍摄功能使用**
   - 拍照与录像
   - 全景拍摄
   - 变焦操作

#### 实操练习（30分钟）
- 负载设备控制
- 拍摄操作练习
- 红外功能使用
- 全景拍摄实践

### 第十五节：媒体库与模型库管理（1学时）
**学习目标：** 掌握媒体资源和模型的管理技能

#### 理论讲解（30分钟）
1. **媒体库管理**
   - 媒体资源查看
   - 文件下载与删除
   - 照片对比功能

2. **模型库管理**
   - 模型类型：正射图、点云模型、倾斜模型
   - 模型预览功能
   - 模型对比分析

#### 实操练习（30分钟）
- 媒体文件管理
- 照片对比操作
- 模型预览查看
- 建模任务监控

### 第十六节：算法库与AI功能（1学时）
**学习目标：** 了解AI算法的应用和配置

#### 理论讲解（30分钟）
1. **算法库功能**
   - 支持的算法模型
   - 适用场景分析
   - 识别内容类型

2. **AI识别配置**
   - 照片回传检测
   - 直播流检测
   - 智识联动设置

#### 实操练习（30分钟）
- 查看算法库
- 配置AI识别
- 测试识别功能
- 分析识别结果

---

## 第三天上午（4学时）：高级功能与综合应用

### 第十七节：飞行申请与审批管理（1学时）
**学习目标：** 掌握飞行申请的流程和审批管理

#### 理论讲解（30分钟）
1. **飞行申请流程**
   - 申请创建步骤
   - 审批流程设置
   - 申请状态跟踪

2. **审批管理功能**
   - 审批规则配置
   - 历史记录查看
   - 权限控制设置

#### 实操练习（30分钟）
- 创建飞行申请
- 设置审批流程
- 处理审批请求
- 查看申请状态

### 第十八节：城市治理与数据分析（1学时）
**学习目标：** 掌握城市治理功能和数据分析技能

#### 理论讲解（30分钟）
1. **城市治理功能**
   - 基本信息统计
   - 区域数据管理
   - 图层管理功能

2. **数据分析应用**
   - 飞行记录分析
   - 事件统计分析
   - 趋势分析方法

#### 实操练习（30分钟）
- 查看统计数据
- 分析区域数据
- 管理图层信息
- 生成分析报告

### 第十九节：系统维护与版本管理（1学时）
**学习目标：** 掌握系统维护和版本管理技能

#### 理论讲解（30分钟）
1. **版本管理功能**
   - 版本文件上传
   - 版本信息管理
   - 设备升级操作

2. **系统维护要点**
   - 日常维护检查
   - 故障排除方法
   - 备份恢复操作

#### 实操练习（30分钟）
- 上传版本文件
- 管理版本信息
- 执行设备升级
- 系统维护检查

### 第二十节：综合实战演练（1学时）
**学习目标：** 综合运用所学知识完成完整的作业流程

#### 综合实战（60分钟）
1. **完整作业流程演练**
   - 任务规划设计
   - 设备准备检查
   - 航线执行监控
   - 数据处理分析
   - 结果输出展示

2. **问题解决与优化**
   - 常见问题处理
   - 操作优化建议
   - 最佳实践分享

---

## 培训考核与认证

### 理论考核（30分钟）
- 系统功能理解
- 操作流程掌握
- 安全规范认知

### 实操考核（60分钟）
- 独立完成任务规划
- 设备操作熟练度
- 数据处理能力

### 认证标准
- 理论考核≥80分
- 实操考核≥85分
- 安全操作规范100%

---

## 培训资料与工具

### 必备资料
1. 智慧城市巡检系统使用手册V1.2.4
2. 培训课件PPT
3. 实操练习手册
4. 快捷键操作卡片

### 实训设备
1. 培训专用电脑（Chrome/Edge浏览器）
2. 模拟机场设备
3. 无人机演示设备
4. 网络环境

### 后续支持
1. 在线技术支持
2. 操作视频教程
3. 用户交流群
4. 定期更新培训

---

**培训机构：** 广州智飞科技有限公司  
**培训版本：** V1.2.4  
**更新日期：** 2025年2月
